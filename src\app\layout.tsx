import type { Metadata } from "next";
import "./globals.css";
import React from "react";
import Navbar from "@/components/navbar";
import { urbanist } from "@/app/fonts";
import TimelineProvider from "./TimelineProvider";
import SmoothScrollProvider from "./SmoothScrollProvider";

export const metadata: Metadata = {
    title: "Kicks | Shoes",
    description: "Generated by create next app",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body className={`antialiased ${urbanist.className}`}>
                <SmoothScrollProvider>
                    <TimelineProvider>
                        <Navbar />
                    </TimelineProvider>
                    {children}
                </SmoothScrollProvider>
            </body>
        </html>
    );
}

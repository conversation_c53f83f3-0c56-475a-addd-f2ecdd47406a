"use client";
import Image from "next/image";
import React, { useRef } from "react";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { FaCartShopping } from "react-icons/fa6";
import { CgMenuRight } from "react-icons/cg";
import { RxCross2 } from "react-icons/rx";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import { FiSearch } from "react-icons/fi";
import { useTimeline } from "@/hooks/useTimeline";

const Navbar = () => {
    const timeline = useTimeline();

    const navRaf = useRef<HTMLDivElement>(null);
    const liRefs = useRef<HTMLLIElement[]>([]);
    const menuRef = useRef<HTMLDivElement>(null);
    const closeMenuRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const elemRef = useRef<HTMLDivElement>(null);

    // For desktop-animation
    useGSAP(() => {
        if (elemRef.current && elemRef.current.children) {
            timeline.from(elemRef.current.children, {
                opacity: 0,
                y: -50,
                duration: 1,
                ease: "power3.out",
                stagger: 0.3,
            });
        }
    }, []);

    // For full screen navbar
    useGSAP(() => {
        const tl = gsap.timeline({ paused: true });

        tl.to(navRaf.current!, {
            right: 0,
            duration: 0.5,
            ease: "power3.out",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
        }).from(
            liRefs.current,
            {
                x: 50,
                opacity: 0,
                stagger: 0.1,
                duration: 0.5,
                ease: "power3.inOut",
                filter: "blur(5px)",
            },
            "-=0.2"
        ); // overlap a bit

        menuRef.current?.addEventListener("click", () => {
            tl.play(0); // restart from beginning every time
        });

        closeMenuRef.current?.addEventListener("click", () => {
            tl.reverse();
        });
    }, []);

    return (
        <div className="relative container px-6! py-4! z-99 overflow-hidden ">
            <div
                ref={elemRef}
                className="flex items-center justify-between gap-4"
            >
                <div className="logo">
                    <Image
                        src={"/logo.svg"}
                        width={100}
                        height={100}
                        alt="logo"
                    />
                </div>
                <div className="search flex-1 max-w-[400px] flex items-center gap-2">
                    <Input
                        ref={inputRef}
                        type="text"
                        className="flex-1 w-full "
                        placeholder="Search for products, brands and more"
                    />
                    <FiSearch
                        size={20}
                        className="cursor-pointer"
                        onClick={() => inputRef.current?.focus()}
                    />
                </div>
                <div className="icons flex items-center gap-6 [&>*]:cursor-pointer ">
                    <div>
                        <FaCartShopping size={20} />
                    </div>
                    <div ref={menuRef}>
                        <CgMenuRight size={25} />
                    </div>
                </div>
            </div>
            <nav
                ref={navRaf}
                className="full-nav fixed top-0 right-[-350px] px-12 py-10 backdrop-blur-lg rounded-l-xl  bg-black/30 h-full  w-[350px] "
            >
                <ul className="[&>li]:list-none [&>li]:cursor-pointer [&>li]:text-4xl gap-6 [&>li]:text-white flex flex-col justify-center h-full">
                    {["Home", "About", "All Products", "Contact"].map(
                        (item, i) => (
                            <li
                                key={i}
                                className="hover:text-gray-300"
                                ref={(el) => {
                                    if (el) liRefs.current[i] = el;
                                }}
                            >
                                <Link href="/">{item}</Link>
                            </li>
                        )
                    )}
                </ul>
                <div ref={closeMenuRef}>
                    <RxCross2
                        className="cursor-pointer text-white absolute top-10 right-10"
                        size={50}
                    />
                </div>
            </nav>
        </div>
    );
};
export default Navbar;

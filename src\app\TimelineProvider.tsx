"use client";
import { TimeLineContext } from "@/context/TimelineContext";
import React, { useRef } from "react";
import gsap from "gsap";

const TimelineProvider = ({ children }: { children: React.ReactNode }) => {
    const timelineRef = useRef(
        gsap.timeline({ defaults: { duration: 1, ease: "power3.out" } })
    );

    return (
        <TimeLineContext.Provider value={timelineRef.current}>
            {children}
        </TimeLineContext.Provider>
    );
};

export default TimelineProvider;

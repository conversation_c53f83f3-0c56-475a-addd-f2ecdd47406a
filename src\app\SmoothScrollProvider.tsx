// components/SmoothScrollProvider.tsx
"use client";

import { useEffect, ReactNode } from "react";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

export default function SmoothScrollProvider({
    children,
}: {
    children: ReactNode;
}) {
    useEffect(() => {
        const lenis = new Lenis({ duration: 1.2 });

        lenis.on("scroll", ScrollTrigger.update);
        gsap.ticker.add((time) => {
            lenis.raf(time * 1000);
        });
        gsap.ticker.lagSmoothing(0);

        return () => lenis.destroy();
    }, []);

    return <>{children}</>;
}

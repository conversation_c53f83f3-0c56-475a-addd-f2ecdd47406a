"use client";

import { useGS<PERSON> } from "@gsap/react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { SplitText } from "gsap/SplitText";
import { useRef } from "react";

export default function BlurTextEffect({
    children,
}: {
    children: React.ReactNode;
}) {
    gsap.registerPlugin(SplitText, ScrollTrigger);
    const textRef = useRef(null);
    useGSAP(() => {
        const splitText = SplitText.create(textRef.current, { type: "words" });

        gsap.from(splitText.words, {
            filter: "blur(5px)",
            duration: 0.8,
            ease: "power4.out",
            opacity: 0,
            stagger: 0.1,
            scrollTrigger: {
                trigger: textRef.current,
                start: "top 80%",
                end: "bottom 20%",
                markers: true,
                scrub: 1, // Smooth animation
            },
        });
    }, []);

    return (
        <>
            <div ref={textRef}>{children}</div>
        </>
    );
}

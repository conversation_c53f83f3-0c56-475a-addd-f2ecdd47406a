"use client";
import React, { useEffect, useState } from "react";

// react icons
import { IoIosSearch } from "react-icons/io";
import { GoLinkExternal } from "react-icons/go";

// data
import { productsData } from "@/data/sample-search-data";
import Image from "next/image";

const SearchBar = () => {
  const [filteredData, setFilteredData] = useState(productsData);
  const [inputText, setInputText] = useState("");
  const [inputFocus, setInputFocus] = useState(false);

  useEffect(() => {
    const filtered = productsData?.filter((product) => {
      if (inputText === "") {
        return productsData;
      } else {
        return product?.name.toLowerCase().includes(inputText);
      }
    });

    setFilteredData(filtered);
  }, [inputText]);

  function truncate(text: string, maxLength: number, ellipsis = "...") {
    if (text?.length <= maxLength) {
      return text;
    }
    return text?.slice(0, maxLength - ellipsis?.length) + ellipsis;
  }

  useEffect(() => {
    const handleClick = (event: any) => {
      if (
        !event.target.closest(".product_search_bar") &&
        !event.target.closest(".product_search_input")
      ) {
        setInputFocus(false);
      }
    };
    document.addEventListener("click", handleClick);
    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, []);

  return (
    <div className="relative w-full sm:w-[80%] max-w-[500px] product_search_input">
      <input
        className="px-4 py-2 dark:border-slate-700 dark:bg-slate-900 dark:text-[#abc2d3] dark:placeholder:text-slate-500 border border-border rounded-md w-full pl-[40px] outline-none focus:border-[#3B9DF8]"
        placeholder="Search..."
        onChange={(e) => setInputText(e.target.value)}
        onClick={() => setInputFocus(true)}
      />
      <IoIosSearch className="absolute dark:text-slate-500 top-[9px] left-2 text-[1.5rem] text-[#adadad]" />

      <div
        className={`${
          inputFocus
            ? "opacity-100 h-auto translate-y-0 mt-2 z-99"
            : "translate-y-[-10px] opacity-0 h-0"
        } product_search_bar bg-white boxShadow w-full transition-all duration-500 dark:bg-slate-900 overflow-hidden flex flex-col rounded-md`}
      >
        {filteredData?.map((product) => (
          <div
            key={product?.id}
            className="flex items-center justify-between w-full px-6 py-4 hover:bg-gray-50 dark:hover:bg-slate-800/50 cursor-pointer rounded-md"
          >
            <div className="flex items-center gap-[10px]">
              <Image
                src={product?.image}
                alt="product/image"
                className="w-[30px] h-[30px] object-cover"
              />
              <h1 className="text-[0.9rem] dark:text-[#abc2d3] sm:text-[1.1rem] text-gray-700 font-[400]">
                {truncate(product?.name, 60)}
              </h1>
            </div>
            <GoLinkExternal className="text-[1.3rem] dark:text-slate-500 text-gray-400" />
          </div>
        ))}

        {!filteredData?.length && (
          <p className="text-[0.9rem] py-3 dark:text-slate-500 text-[#a0a0a0] text-center">
            No search matched!
          </p>
        )}
      </div>
    </div>
  );
};

export default SearchBar;

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2554 0.0037 106.6574);
  --card: oklch(0.9851 0 0);
  --card-foreground: oklch(0.2554 0.0037 106.6574);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2554 0.0037 106.6574);
  --primary: oklch(0.7941 0.1610 67.4927);
  --primary-foreground: oklch(0.2554 0.0037 106.6574);
  --secondary: oklch(0.5649 0.1870 268.8470);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.9851 0 0);
  --muted-foreground: oklch(0.5445 0.0031 106.5005);
  --accent: oklch(0.9268 0.0053 106.5034);
  --accent-foreground: oklch(0.2554 0.0037 106.6574);
  --destructive: oklch(0.6137 0.2039 25.5645);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9268 0.0053 106.5034);
  --input: oklch(0.9268 0.0053 106.5034);
  --ring: oklch(0.7941 0.1610 67.4927);
  --chart-1: oklch(0.7941 0.1610 67.4927);
  --chart-2: oklch(0.5649 0.1870 268.8470);
  --chart-3: oklch(0.5445 0.0031 106.5005);
  --chart-4: oklch(0.2554 0.0037 106.6574);
  --chart-5: oklch(0.7058 0 0);
  --sidebar: oklch(0.9851 0 0);
  --sidebar-foreground: oklch(0.2554 0.0037 106.6574);
  --sidebar-primary: oklch(0.7941 0.1610 67.4927);
  --sidebar-primary-foreground: oklch(0.2554 0.0037 106.6574);
  --sidebar-accent: oklch(0.9268 0.0053 106.5034);
  --sidebar-accent-foreground: oklch(0.2554 0.0037 106.6574);
  --sidebar-border: oklch(0.9268 0.0053 106.5034);
  --sidebar-ring: oklch(0.7941 0.1610 67.4927);
  --font-sans: Inter, sans-serif;
  --font-serif: Inter, sans-serif;
  --font-mono: Inter, sans-serif;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0px 4px 8px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow-md: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px hsl(0 0% 0% / 0.05);
  --shadow-lg: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px hsl(0 0% 0% / 0.05);
  --shadow-xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px hsl(0 0% 0% / 0.05);
  --shadow-2xl: 0px 4px 8px 0px hsl(0 0% 0% / 0.13);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2554 0.0037 106.6574);
  --foreground: oklch(0.9851 0 0);
  --card: oklch(0.2554 0.0037 106.6574);
  --card-foreground: oklch(0.9851 0 0);
  --popover: oklch(0.2554 0.0037 106.6574);
  --popover-foreground: oklch(0.9851 0 0);
  --primary: oklch(0.7941 0.1610 67.4927);
  --primary-foreground: oklch(0.2554 0.0037 106.6574);
  --secondary: oklch(0.5649 0.1870 268.8470);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.5445 0.0031 106.5005);
  --muted-foreground: oklch(0.9268 0.0053 106.5034);
  --accent: oklch(0.5649 0.1870 268.8470);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6786 0.2095 24.6583);
  --destructive-foreground: oklch(0.9851 0 0);
  --border: oklch(0.5445 0.0031 106.5005);
  --input: oklch(0.5445 0.0031 106.5005);
  --ring: oklch(0.7941 0.1610 67.4927);
  --chart-1: oklch(0.7941 0.1610 67.4927);
  --chart-2: oklch(0.5649 0.1870 268.8470);
  --chart-3: oklch(0.9268 0.0053 106.5034);
  --chart-4: oklch(0.5445 0.0031 106.5005);
  --chart-5: oklch(1.0000 0 0);
  --sidebar: oklch(0.2554 0.0037 106.6574);
  --sidebar-foreground: oklch(0.9851 0 0);
  --sidebar-primary: oklch(0.7941 0.1610 67.4927);
  --sidebar-primary-foreground: oklch(0.2554 0.0037 106.6574);
  --sidebar-accent: oklch(0.5649 0.1870 268.8470);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.5445 0.0031 106.5005);
  --sidebar-ring: oklch(0.7941 0.1610 67.4927);
  --font-sans: Inter, sans-serif;
  --font-serif: Inter, sans-serif;
  --font-mono: Inter, sans-serif;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

:root {
  --container-max: 1440px;
  --container-gutter: clamp(16px, 4vw, 32px);
}

.container {
  box-sizing: border-box;
  max-width: var(--container-max);
  width: min(100% - 2rem, var(--container-max));
  margin-inline: auto;
  padding-inline: var(--container-gutter);
  padding-block: 0;
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utils {
  .text-stroke {
    -webkit-text-stroke: 1.5px #0c315f3b;
    color: transparent;

  }
} 


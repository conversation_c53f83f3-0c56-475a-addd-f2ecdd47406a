"use client";

import { poppins } from "@/app/fonts";
import React, { useRef } from "react";
import Image from "next/image";
import { useGSAP } from "@gsap/react";
import { useTimeline } from "@/hooks/useTimeline";
import gsap from "gsap";
import { SplitText } from "gsap/SplitText";

gsap.registerPlugin(SplitText);

/**
 * Hero component for displaying the main promotional section
 * Features animated text, product image, and tagline
 */
const Hero = () => {
    // Reference to the hero section element
    const heroRef = useRef<HTMLSelectElement>(null);
    // Reference to the text ref for split animation
    const textRef = useRef<HTMLDivElement>(null);
    const paraRef = useRef<HTMLDivElement>(null);

    const imageRef = useRef<HTMLImageElement>(null);

    // Get current year for dynamic text
    const year = new Date().getFullYear();
    // Access GSAP timeline for animations
    const timeline = useTimeline();
    // GSAP animation for hero heading elements
    useGSAP(
        () => {
            const splitText = new SplitText(textRef.current, { type: "chars" });
            const splitPara = new SplitText(paraRef.current, { type: "words" });

            if (heroRef.current) {
                timeline
                    .from(".stroke-heading", {
                        opacity: 0,
                        y: -50,
                        duration: 1,
                        ease: "power3.out",
                        stagger: 0.3,
                    })
                    .from(".gradient-text", {
                        opacity: 0,
                        y: -50,
                        duration: 1,
                        ease: "bounce.inOut",
                        stagger: 0.3,
                    })
                    .from(splitText.chars, {
                        filter: "blur(5px)",
                        opacity: 0,
                        x: -50,
                        duration: 0.6,
                        ease: "circ.inOut",
                        stagger: 0.1,
                    })
                    .from(
                        imageRef.current,
                        {
                            filter: "blur(5px)",
                            duration: 0.5,
                            ease: "power4.out",
                        },
                        "-=1"
                    )
                    .from(splitPara.words, {
                        filter: "blur(5px)",
                        duration: 0.5,
                        ease: "power4.out",
                        opacity: 0,
                        stagger: 0.1,
                    });
            }
        },
        { scope: heroRef }
    );

    return (
        <section
            ref={heroRef}
            className=" mt-10 md:mt-0 relative container mx-auto h-auto flex flex-col items-center justify-center"
        >
            {/* Outline Text */}
            <h2
                className={`${poppins.className} stroke-heading whitespace-nowrap translate-y-1/2 text-center font-extrabold uppercase text-[12vw] sm:text-[10vw] md:text-[10vw] text-stroke leading-none`}
            >
                The New {year}
            </h2>

            {/* Gradient Text */}
            <h1
                className={`${poppins.className} gradient-text text-center font-extrabold uppercase bg-[linear-gradient(to_right,#4280CF_0%,#3975C0_8%,#0C315F_45%,#0A2444_59%,#03234B_82%,#4280CF_100%)] bg-clip-text text-transparent text-[14vw] sm:text-[12vw] md:text-[12vw] leading-none whitespace-nowrap`}
            >
                Air Jordan
            </h1>

            {/* Product Image */}
            <div className="w-[120%] max-w-[900px] md:-translate-y-[30%]   -translate-y-[20%] ">
                <Image
                    ref={imageRef}
                    src="/images/hero-product.png"
                    alt="Air Jordan Shoe"
                    width={1000}
                    height={900}
                    className="w-[120%] h-auto object-cover"
                    priority
                />
            </div>
            <div
                className={`heading text-[40px] -translate-y-[50%] md:-translate-y-[200%] text-center text-[#000000]  md:text-[56px]  font-extrabold ${poppins.className}`}
            >
                <div className="heading" ref={textRef}>
                    Just do it
                </div>
                <div
                    className="para text-sm text-[#959799] font-medium"
                    ref={paraRef}
                >
                    Captures messages that stands for more than just sport.{" "}
                </div>
            </div>
        </section>
    );
};

export default Hero;

import React from "react";
import Image from "next/image";
import { But<PERSON> } from "./ui/button";
import BlurTextEffect from "./utils/blur-text";

const FeatureProduct = () => {
    return (
        <div className="container mx-auto h-full flex w-full flex-col items-center justify-center p-0!">
            {/* Main heading */}
            <div className="main-heading text-[12vw] sm:text-[8vw] md:text-[6vw] lg:text-[5vw] font-extrabold uppercase text-center">
                <BlurTextEffect>
                    Do it <span className="text-secondary">right</span>
                </BlurTextEffect>
            </div>

            {/* Background section */}
            <div className="relative container w-full overflow-hidden h-full min-h-[400px] sm:min-h-[500px] lg:min-h-[720px] bg-no-repeat bg-cover bg-[url('/images/featured-product.png')] rounded-xl flex flex-col lg:block">
                {/* Horizontal label for mobile */}
                <div className="bg-gray-800 text-white rounded-t-xl py-2 px-4 text-center block lg:hidden">
                    <span className="text-xs sm:text-sm tracking-wide">
                        Nike product of the year
                    </span>
                </div>

                {/* Vertical label for desktop */}
                <div
                    className="hidden lg:block bg-gray-800 text-white rounded-xl absolute top-1/2 -translate-y-1/2 left-0 p-4 text-center"
                    style={{
                        writingMode: "vertical-rl",
                        whiteSpace: "nowrap",
                    }}
                >
                    <span className="text-sm tracking-wide">
                        Nike product of the year
                    </span>
                </div>

                {/* Mobile stacked layout */}
                <div className="flex flex-col lg:block flex-1">
                    {/* Text content */}
                    <div className="content text-white order-2 lg:order-none lg:absolute lg:bottom-10 lg:left-10 flex flex-col gap-3 max-w-full sm:max-w-[60%]">
                        <h1 className="content-heading text-2xl sm:text-3xl font-bold">
                            Nike Air Max
                        </h1>
                        <p className="text-sm sm:text-base">
                            Nike introducing the new air max for everyone&apos;s
                            comfort
                        </p>
                        <Button className="w-fit text-sm sm:text-base">
                            Buy Now
                        </Button>
                    </div>

                    {/* Images */}
                    <div className="images order-1 lg:order-none flex flex-row lg:flex-col gap-3 justify-center p-5 lg:absolute lg:bottom-10 lg:right-10">
                        <Image
                            src="/images/featured-product-subImage1.png"
                            alt="Air Jordan Shoe"
                            width={250}
                            height={250}
                            className="w-[100px] sm:w-[120px] md:w-[140px] lg:w-[120px] h-auto object-cover rounded-lg"
                            priority
                        />
                        <Image
                            src="/images/featured-product-subImage2.png"
                            alt="Air Jordan Shoe"
                            width={250}
                            height={250}
                            className="w-[100px] sm:w-[120px] md:w-[140px] lg:w-[120px] h-auto object-cover rounded-lg"
                            priority
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FeatureProduct;
